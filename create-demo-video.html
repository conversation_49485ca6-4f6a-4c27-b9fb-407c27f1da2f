<!DOCTYPE html>
<html>
<head>
    <title>إنشاء فيديو تجريبي</title>
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: #1a1a2e;
            color: white;
            padding: 20px;
            text-align: center;
        }
        canvas {
            border: 2px solid #60a5fa;
            border-radius: 10px;
            margin: 20px;
        }
        button {
            background: linear-gradient(135deg, #3b82f6, #60a5fa);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            font-size: 16px;
            cursor: pointer;
            margin: 10px;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(96, 165, 250, 0.4);
        }
    </style>
</head>
<body>
    <h1>إنشاء فيديو تجريبي للمشروع</h1>
    <canvas id="canvas" width="800" height="600"></canvas>
    <br>
    <button onclick="startAnimation()">بدء الرسوم المتحركة</button>
    <button onclick="stopAnimation()">إيقاف</button>
    <button onclick="downloadVideo()">تحميل الفيديو</button>
    
    <script>
        const canvas = document.getElementById('canvas');
        const ctx = canvas.getContext('2d');
        let animationId;
        let mediaRecorder;
        let recordedChunks = [];
        
        // إعدادات الرسوم المتحركة
        let frame = 0;
        let particles = [];
        
        // إنشاء جسيمات
        for (let i = 0; i < 50; i++) {
            particles.push({
                x: Math.random() * canvas.width,
                y: Math.random() * canvas.height,
                vx: (Math.random() - 0.5) * 2,
                vy: (Math.random() - 0.5) * 2,
                size: Math.random() * 3 + 1,
                color: `hsl(${Math.random() * 60 + 200}, 70%, 60%)`
            });
        }
        
        function animate() {
            // مسح الشاشة
            ctx.fillStyle = 'rgba(26, 26, 46, 0.1)';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // رسم الخلفية
            const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
            gradient.addColorStop(0, '#1a1a2e');
            gradient.addColorStop(1, '#16213e');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // رسم الجسيمات
            particles.forEach(particle => {
                particle.x += particle.vx;
                particle.y += particle.vy;
                
                if (particle.x < 0 || particle.x > canvas.width) particle.vx *= -1;
                if (particle.y < 0 || particle.y > canvas.height) particle.vy *= -1;
                
                ctx.beginPath();
                ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
                ctx.fillStyle = particle.color;
                ctx.fill();
            });
            
            // رسم النص
            ctx.font = 'bold 48px Arial';
            ctx.fillStyle = '#60a5fa';
            ctx.textAlign = 'center';
            ctx.fillText('ITQNWEB Business', canvas.width / 2, canvas.height / 2 - 50);
            
            ctx.font = 'bold 24px Arial';
            ctx.fillStyle = '#ffffff';
            ctx.fillText('Dream. Build', canvas.width / 2, canvas.height / 2 + 20);
            
            ctx.font = '18px Arial';
            ctx.fillStyle = '#a0a0a0';
            ctx.fillText('موقع شركة تقنية احترافي', canvas.width / 2, canvas.height / 2 + 60);
            
            // رسم دائرة متحركة
            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2 + 120;
            const radius = 30 + Math.sin(frame * 0.1) * 10;
            
            ctx.beginPath();
            ctx.arc(centerX, centerY, radius, 0, Math.PI * 2);
            ctx.strokeStyle = '#60a5fa';
            ctx.lineWidth = 3;
            ctx.stroke();
            
            frame++;
            animationId = requestAnimationFrame(animate);
        }
        
        function startAnimation() {
            // بدء التسجيل
            const stream = canvas.captureStream(30);
            mediaRecorder = new MediaRecorder(stream, {
                mimeType: 'video/webm;codecs=vp9'
            });
            
            mediaRecorder.ondataavailable = function(event) {
                if (event.data.size > 0) {
                    recordedChunks.push(event.data);
                }
            };
            
            mediaRecorder.start();
            animate();
            
            // إيقاف التسجيل بعد 10 ثوان
            setTimeout(() => {
                stopAnimation();
            }, 10000);
        }
        
        function stopAnimation() {
            if (animationId) {
                cancelAnimationFrame(animationId);
            }
            if (mediaRecorder && mediaRecorder.state !== 'inactive') {
                mediaRecorder.stop();
            }
        }
        
        function downloadVideo() {
            if (recordedChunks.length > 0) {
                const blob = new Blob(recordedChunks, {
                    type: 'video/webm'
                });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = 'intro.webm';
                a.click();
                URL.revokeObjectURL(url);
            } else {
                alert('لم يتم تسجيل أي فيديو بعد!');
            }
        }
    </script>
</body>
</html>
