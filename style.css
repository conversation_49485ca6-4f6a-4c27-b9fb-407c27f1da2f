/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-purple: #8A4FFF;
    --secondary-purple: #6B46C1;
    --light-blue: #9B8FFF;
    --dark-purple: #4C1D95;
    --gradient-primary: linear-gradient(135deg, #8A4FFF 0%, #6B46C1 50%, #9B8FFF 100%);
    --gradient-secondary: linear-gradient(45deg, #4C1D95 0%, #8A4FFF 100%);
    --text-light: #F8FAFC;
    --text-dark: #1E293B;
    --bg-dark: #0D0B1F;
    --bg-light: #F1F5F9;
}


body {
    font-family: 'Cairo', sans-serif;
    line-height: 1.6;
    color: var(--text-light);
    background: linear-gradient(180deg, #0D0B1F 0%, #1E133F 100%);
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Loading Screen */
#loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    transition: opacity 0.5s ease;
}

.loader {
    text-align: center;
}

.logo-container {
    position: relative;
    margin-bottom: 30px;
}

.logo-circle {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    animation: pulse 2s infinite;
}

.circuit-pattern {
    width: 60px;
    height: 60px;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="3" fill="%23ffffff" opacity="0.8"/><circle cx="80" cy="20" r="3" fill="%23ffffff" opacity="0.8"/><circle cx="50" cy="50" r="5" fill="%23ffffff"/><circle cx="20" cy="80" r="3" fill="%23ffffff" opacity="0.8"/><circle cx="80" cy="80" r="3" fill="%23ffffff" opacity="0.8"/><path d="M20 20 L50 50 L80 20 M20 80 L50 50 L80 80" stroke="%23ffffff" stroke-width="1" fill="none" opacity="0.6"/></svg>') center/contain no-repeat;
    animation: rotate 3s linear infinite;
}

.floating-letters {
    position: absolute;
    top: -40px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 15px;
}

.letter {
    font-size: 24px;
    font-weight: 900;
    color: white;
    animation: float 2s ease-in-out infinite;
}

.letter:nth-child(1) { animation-delay: 0s; }
.letter:nth-child(2) { animation-delay: 0.2s; }
.letter:nth-child(3) { animation-delay: 0.4s; }
.letter:nth-child(4) { animation-delay: 0.6s; }

.loading-text {
    font-size: 18px;
    font-weight: 600;
    color: white;
    letter-spacing: 3px;
    animation: fadeInOut 2s infinite;
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(15, 23, 42, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    z-index: 1000;
    transition: all 0.3s ease;
}

.navbar.scrolled {
    background: rgba(15, 23, 42, 0.98);
    box-shadow: 0 4px 20px rgba(138, 79, 255, 0.3);
}


.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    max-width: 1200px;
    margin: 0 auto;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: 10px;
}

.nav-logo .logo-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--gradient-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    animation: glow 2s ease-in-out infinite alternate;
}

.nav-logo .circuit-pattern {
    width: 20px;
    height: 20px;
}

.logo-text {
    font-size: 24px;
    font-weight: 900;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 30px;
}

.nav-link {
    color: var(--text-light);
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--gradient-primary);
    transition: width 0.3s ease;
}

.nav-link:hover::after {
    width: 100%;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background: var(--text-light);
    transition: all 0.3s ease;
}

/* Language Switcher */
.language-switcher {
    display: flex;
    align-items: center;
    margin-left: 20px;
}

.lang-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 25px;
    padding: 8px 15px;
    color: var(--text-light);
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    font-weight: 500;
    backdrop-filter: blur(10px);
}

.lang-btn:hover {
    background: var(--gradient-primary);
    border-color: transparent;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(107, 70, 193, 0.4);
}

.lang-btn i {
    font-size: 16px;
}

#current-lang {
    font-weight: 600;
}

/* Language Direction Styles */
[dir="ltr"] .nav-container {
    flex-direction: row;
}

[dir="ltr"] .language-switcher {
    margin-left: 20px;
    margin-right: 0;
}

[dir="ltr"] .hero-buttons {
    flex-direction: row;
}

[dir="ltr"] .contact-item:hover {
    transform: translateX(-10px);
}

[dir="rtl"] .contact-item:hover {
    transform: translateX(10px);
}

[dir="ltr"] .footer-content {
    text-align: left;
}

[dir="ltr"] .footer-text {
    text-align: center;
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    z-index: -2;
}

.floating-shapes {
    position: absolute;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.shape {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    animation: floatShapes 20s infinite linear;
}

.shape-1 {
    width: 100px;
    height: 100px;
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.shape-2 {
    width: 150px;
    height: 150px;
    top: 60%;
    right: 10%;
    animation-delay: -5s;
}

.shape-3 {
    width: 80px;
    height: 80px;
    top: 80%;
    left: 20%;
    animation-delay: -10s;
}

.shape-4 {
    width: 120px;
    height: 120px;
    top: 30%;
    right: 30%;
    animation-delay: -15s;
}

.hero-content {
    text-align: center;
    z-index: 1;
}

.hero-logo .main-logo {
    position: relative;
    margin-bottom: 30px;
}

.hero-logo .logo-circle {
    width: 200px;
    height: 200px;
    margin: 0 auto 20px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 3px solid rgba(255, 255, 255, 0.2);
    animation: heroLogoPulse 3s ease-in-out infinite;
}

.hero-logo .circuit-pattern {
    width: 100px;
    height: 100px;
}

.hero-logo .floating-letters {
    top: -60px;
    gap: 20px;
}

.hero-logo .letter {
    font-size: 36px;
    text-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
}

.hero-title {
    font-size: 4rem;
    font-weight: 900;
    margin-bottom: 20px;
    letter-spacing: 2px;
}

.gradient-text {
    background: linear-gradient(45deg, #ffffff, #60A5FA, #ffffff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradientShift 3s ease-in-out infinite;
}

.hero-subtitle {
    font-size: 1.5rem;
    margin-bottom: 40px;
    opacity: 0.9;
}

.hero-buttons {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
}

.btn {
    padding: 15px 30px;
    border: none;
    border-radius: 50px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    position: relative;
    overflow: hidden;
}

.btn-primary {
    background: var(--gradient-primary);
    color: white;
    box-shadow: 0 10px 30px rgba(107, 70, 193, 0.4);
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 40px rgba(138, 79, 255, 0.6);
}


.btn-secondary {
    background: transparent;
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-3px);
}

.scroll-indicator {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    animation: bounce 2s infinite;
}

.scroll-arrow {
    width: 30px;
    height: 30px;
    border-right: 2px solid white;
    border-bottom: 2px solid white;
    transform: rotate(45deg);
}

/* Animations */
@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

@keyframes fadeInOut {
    0%, 100% { opacity: 0.7; }
    50% { opacity: 1; }
}

@keyframes glow {
    from { box-shadow: 0 0 20px rgba(107, 70, 193, 0.5); }
    to { box-shadow: 0 0 30px rgba(139, 92, 246, 0.8); }
}

@keyframes floatShapes {
    0% { transform: translateY(100vh) rotate(0deg); }
    100% { transform: translateY(-100px) rotate(360deg); }
}

@keyframes heroLogoPulse {
    0%, 100% { transform: scale(1); box-shadow: 0 0 30px rgba(255, 255, 255, 0.3); }
    50% { transform: scale(1.05); box-shadow: 0 0 50px rgba(255, 255, 255, 0.5); }
}

@keyframes gradientShift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateX(-50%) translateY(0); }
    40% { transform: translateX(-50%) translateY(-10px); }
    60% { transform: translateX(-50%) translateY(-5px); }
}

/* Section Styles */
.section-header {
    text-align: center;
    margin-bottom: 60px;
}

.section-title {
    font-size: 3rem;
    font-weight: 900;
    margin-bottom: 20px;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.section-subtitle {
    font-size: 1.2rem;
    opacity: 0.8;
    max-width: 600px;
    margin: 0 auto;
}

/* About Section */
.about {
    padding: 100px 0;
    background: var(--bg-dark);
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
}

.about-text h3 {
    font-size: 2.5rem;
    margin-bottom: 20px;
    color: var(--light-blue);
}

.about-text p {
    font-size: 1.1rem;
    line-height: 1.8;
    margin-bottom: 40px;
    opacity: 0.9;
}

.stats {
    display: flex;
    gap: 40px;
    flex-wrap: wrap;
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 3rem;
    font-weight: 900;
    color: var(--light-blue);
    margin-bottom: 5px;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.8;
}

.tech-stack {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
}

.tech-item {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 20px;
    text-align: center;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.tech-item:hover {
    transform: translateY(-5px);
    background: rgba(107, 70, 193, 0.1);
    border-color: var(--secondary-purple);
}

.tech-item i {
    font-size: 2.5rem;
    margin-bottom: 10px;
    color: var(--light-blue);
}

.tech-item span {
    display: block;
    font-weight: 600;
}

/* Services Section */
.services {
    padding: 100px 0;
    background: linear-gradient(135deg, var(--bg-dark) 0%, #1E293B 100%);
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.service-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 40px 30px;
    text-align: center;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    opacity: 0;
    transition: all 0.3s ease;
    z-index: -1;
}

.service-card:hover::before {
    left: 0;
    opacity: 0.1;
}

.service-card:hover {
    transform: translateY(-10px);
    border-color: var(--secondary-purple);
    box-shadow: 0 20px 40px rgba(107, 70, 193, 0.3);
}

.service-icon {
    width: 80px;
    height: 80px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    font-size: 2rem;
    color: white;
}

.service-card h3 {
    font-size: 1.5rem;
    margin-bottom: 15px;
    color: var(--light-blue);
}

.service-card p {
    margin-bottom: 20px;
    opacity: 0.9;
    line-height: 1.6;
}

.service-features {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    justify-content: center;
}

.service-features span {
    background: rgba(96, 165, 250, 0.2);
    color: var(--light-blue);
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 0.9rem;
    border: 1px solid rgba(96, 165, 250, 0.3);
}

/* Portfolio Section */
.portfolio {
    padding: 100px 0;
    background: var(--bg-dark);
}

.portfolio-filters {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-bottom: 50px;
    flex-wrap: wrap;
}

.filter-btn {
    background: transparent;
    border: 2px solid rgba(255, 255, 255, 0.2);
    color: var(--text-light);
    padding: 10px 25px;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.filter-btn.active,
.filter-btn:hover {
    background: var(--gradient-primary);
    border-color: transparent;
    transform: translateY(-2px);
}

.portfolio-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
}

.portfolio-item {
    position: relative;
    border-radius: 15px;
    overflow: hidden;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.portfolio-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
}

.portfolio-image {
    height: 250px;
    overflow: hidden;
    position: relative;
}

.project-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.portfolio-item:hover .project-image {
    transform: scale(1.05);
}

.portfolio-video-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.portfolio-item:hover .portfolio-video-overlay {
    opacity: 1;
}

.play-btn {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: var(--gradient-primary);
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.play-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 5px 15px rgba(96, 165, 250, 0.4);
}

.placeholder-image {
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    color: white;
    font-weight: 600;
}

.web-placeholder::before {
    content: '🌐 موقع ويب';
}

.ecommerce-placeholder::before {
    content: '🛒 متجر إلكتروني';
}

.learning-placeholder::before {
    content: '📚 منصة تعليمية';
}

.restaurant-placeholder::before {
    content: '🍽️ موقع مطعم';
}

.portfolio-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.9));
    padding: 30px 20px 20px;
    transform: translateY(100%);
    transition: all 0.3s ease;
}

.portfolio-item:hover .portfolio-overlay {
    transform: translateY(0);
}

.portfolio-overlay h4 {
    font-size: 1.3rem;
    margin-bottom: 10px;
    color: var(--light-blue);
}

.portfolio-overlay p {
    margin-bottom: 15px;
    opacity: 0.9;
}

.project-tech {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 15px;
}

.tech-tag {
    background: rgba(96, 165, 250, 0.2);
    color: var(--light-blue);
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
    border: 1px solid rgba(96, 165, 250, 0.3);
}

.portfolio-links {
    display: flex;
    gap: 10px;
}

.portfolio-link {
    width: 40px;
    height: 40px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-decoration: none;
    transition: all 0.3s ease;
}

.portfolio-link:hover {
    transform: scale(1.1);
}

/* Video Modal */
.video-modal {
    display: none;
    position: fixed;
    z-index: 10000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.9);
    align-items: center;
    justify-content: center;
}

.video-modal-content {
    position: relative;
    max-width: 90%;
    max-height: 90%;
    background: var(--bg-dark);
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.5);
}

.video-close {
    position: absolute;
    top: 15px;
    right: 20px;
    color: white;
    font-size: 2rem;
    font-weight: bold;
    cursor: pointer;
    z-index: 10001;
    background: rgba(0, 0, 0, 0.5);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.video-close:hover {
    background: rgba(255, 0, 0, 0.7);
    transform: scale(1.1);
}

.video-modal video {
    width: 100%;
    height: auto;
    max-height: 80vh;
    display: block;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hamburger {
        display: flex;
    }

    .nav-menu {
        position: fixed;
        top: 70px;
        right: -100%;
        width: 100%;
        height: calc(100vh - 70px);
        background: rgba(15, 23, 42, 0.98);
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        padding-top: 50px;
        transition: right 0.3s ease;
    }

    .nav-menu.active {
        right: 0;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-subtitle {
        font-size: 1.2rem;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .hero-logo .logo-circle {
        width: 150px;
        height: 150px;
    }

    .hero-logo .circuit-pattern {
        width: 75px;
        height: 75px;
    }

    .hero-logo .letter {
        font-size: 24px;
    }

    .about-content {
        grid-template-columns: 1fr;
        gap: 40px;
    }

    .stats {
        justify-content: center;
    }

    .tech-stack {
        grid-template-columns: repeat(2, 1fr);
    }

    .section-title {
        font-size: 2.5rem;
    }

    .portfolio-grid {
        grid-template-columns: 1fr;
    }

    .language-switcher {
        margin-left: 0;
        margin-right: 0;
        margin-top: 10px;
    }

    .nav-container {
        flex-wrap: wrap;
    }

    [dir="ltr"] .nav-menu {
        left: -100%;
        right: auto;
    }

    [dir="ltr"] .nav-menu.active {
        left: 0;
    }
}

/* Contact Section */
.contact {
    padding: 100px 0;
    background: linear-gradient(135deg, #1E293B 0%, var(--bg-dark) 100%);
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: start;
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 20px;
    padding: 25px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.contact-item:hover {
    transform: translateX(10px);
    background: rgba(107, 70, 193, 0.1);
    border-color: var(--secondary-purple);
}

.contact-icon {
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    flex-shrink: 0;
}

.contact-details h4 {
    font-size: 1.2rem;
    margin-bottom: 5px;
    color: var(--light-blue);
}

.contact-details p {
    opacity: 0.9;
    font-size: 1rem;
}

.contact-form {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 40px;
    backdrop-filter: blur(10px);
}

.form-group {
    margin-bottom: 25px;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 15px 20px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    color: var(--text-light);
    font-size: 1rem;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--secondary-purple);
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 0 20px rgba(107, 70, 193, 0.3);
}

.form-group input::placeholder,
.form-group textarea::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

/* Footer */
.footer {
    background: var(--bg-dark);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding: 60px 0 30px;
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 40px;
    flex-wrap: wrap;
    gap: 30px;
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: 15px;
}

.footer-logo .logo-circle {
    width: 50px;
    height: 50px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: glow 2s ease-in-out infinite alternate;
}

.footer-logo .circuit-pattern {
    width: 25px;
    height: 25px;
}

.footer-logo .logo-text {
    font-size: 1.5rem;
    font-weight: 900;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.footer-text {
    text-align: center;
    flex-grow: 1;
}

.footer-text p:first-child {
    font-size: 1.2rem;
    font-weight: 700;
    letter-spacing: 2px;
    margin-bottom: 5px;
    color: var(--light-blue);
}

.footer-text p:last-child {
    opacity: 0.8;
    font-size: 0.9rem;
}

.social-links {
    display: flex;
    gap: 15px;
}

.social-links a {
    width: 45px;
    height: 45px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-light);
    text-decoration: none;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.social-links a:hover {
    background: var(--gradient-primary);
    border-color: transparent;
    transform: translateY(-3px);
    box-shadow: 0 10px 20px rgba(107, 70, 193, 0.4);
}

.footer-bottom {
    text-align: center;
    padding-top: 30px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    opacity: 0.7;
}

/* Additional Responsive Styles */
@media (max-width: 768px) {
    .contact-content {
        grid-template-columns: 1fr;
        gap: 40px;
    }

    .contact-form {
        padding: 30px 20px;
    }

    .footer-content {
        flex-direction: column;
        text-align: center;
    }

    .footer-text {
        order: -1;
    }
}

.site-logo, .footer-logo-img {
    max-width: 120px;
    height: auto;
    display: block;
    margin: 0 auto;
}

.site-logo, .footer-logo-img {
    filter: drop-shadow(0 0 10px rgba(138, 79, 255, 0.6));
}
