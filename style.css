/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-purple: #8A4FFF;
    --secondary-purple: #6B46C1;
    --light-blue: #9B8FFF;
    --dark-purple: #4C1D95;
    --gradient-primary: linear-gradient(135deg, #8A4FFF 0%, #6B46C1 50%, #9B8FFF 100%);
    --gradient-secondary: linear-gradient(45deg, #4C1D95 0%, #8A4FFF 100%);
    --text-light: #F8FAFC;
    --text-dark: #1E293B;
    --bg-dark: #0D0B1F;
    --bg-light: #F1F5F9;
}


body {
    font-family: 'Cairo', sans-serif;
    line-height: 1.6;
    color: var(--text-light);
    background: linear-gradient(180deg, #0D0B1F 0%, #1E133F 100%);
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Loading Screen */
#loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    transition: opacity 0.5s ease;
}

.loader {
    text-align: center;
}

.logo-container {
    position: relative;
    margin-bottom: 30px;
}

.logo-circle {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    animation: pulse 2s infinite;
}

.circuit-pattern {
    width: 60px;
    height: 60px;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="3" fill="%23ffffff" opacity="0.8"/><circle cx="80" cy="20" r="3" fill="%23ffffff" opacity="0.8"/><circle cx="50" cy="50" r="5" fill="%23ffffff"/><circle cx="20" cy="80" r="3" fill="%23ffffff" opacity="0.8"/><circle cx="80" cy="80" r="3" fill="%23ffffff" opacity="0.8"/><path d="M20 20 L50 50 L80 20 M20 80 L50 50 L80 80" stroke="%23ffffff" stroke-width="1" fill="none" opacity="0.6"/></svg>') center/contain no-repeat;
    animation: rotate 3s linear infinite;
}

.floating-letters {
    position: absolute;
    top: -40px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 15px;
}

.letter {
    font-size: 24px;
    font-weight: 900;
    color: white;
    animation: float 2s ease-in-out infinite;
}

.letter:nth-child(1) { animation-delay: 0s; }
.letter:nth-child(2) { animation-delay: 0.2s; }
.letter:nth-child(3) { animation-delay: 0.4s; }
.letter:nth-child(4) { animation-delay: 0.6s; }

.loading-text {
    font-size: 18px;
    font-weight: 600;
    color: white;
    letter-spacing: 3px;
    animation: fadeInOut 2s infinite;
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(15, 23, 42, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    z-index: 1000;
    transition: all 0.3s ease;
}

.navbar.scrolled {
    background: rgba(15, 23, 42, 0.98);
    box-shadow: 0 4px 20px rgba(138, 79, 255, 0.3);
}


.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    max-width: 1200px;
    margin: 0 auto;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: 10px;
}

.nav-logo .logo-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--gradient-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    animation: glow 2s ease-in-out infinite alternate;
}

.nav-logo .circuit-pattern {
    width: 20px;
    height: 20px;
}

.logo-text {
    font-size: 24px;
    font-weight: 900;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 30px;
}

.nav-link {
    color: var(--text-light);
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--gradient-primary);
    transition: width 0.3s ease;
}

.nav-link:hover::after {
    width: 100%;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background: var(--text-light);
    transition: all 0.3s ease;
}

/* Language Switcher */
.language-switcher {
    display: flex;
    align-items: center;
    margin-left: 20px;
}

.lang-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 25px;
    padding: 8px 15px;
    color: var(--text-light);
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    font-weight: 500;
    backdrop-filter: blur(10px);
}

.lang-btn:hover {
    background: var(--gradient-primary);
    border-color: transparent;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(107, 70, 193, 0.4);
}

.lang-btn i {
    font-size: 16px;
}

#current-lang {
    font-weight: 600;
}

/* Language Direction Styles */
[dir="ltr"] .nav-container {
    flex-direction: row;
}

[dir="ltr"] .language-switcher {
    margin-left: 20px;
    margin-right: 0;
}

[dir="ltr"] .hero-buttons {
    flex-direction: row;
}

[dir="ltr"] .contact-item:hover {
    transform: translateX(-10px);
}

[dir="rtl"] .contact-item:hover {
    transform: translateX(10px);
}

[dir="ltr"] .footer-content {
    text-align: left;
}

[dir="ltr"] .footer-text {
    text-align: center;
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    z-index: -2;
}

.floating-shapes {
    position: absolute;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.shape {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    animation: floatShapes 20s infinite linear;
}

.shape-1 {
    width: 100px;
    height: 100px;
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.shape-2 {
    width: 150px;
    height: 150px;
    top: 60%;
    right: 10%;
    animation-delay: -5s;
}

.shape-3 {
    width: 80px;
    height: 80px;
    top: 80%;
    left: 20%;
    animation-delay: -10s;
}

.shape-4 {
    width: 120px;
    height: 120px;
    top: 30%;
    right: 30%;
    animation-delay: -15s;
}

.hero-content {
    text-align: center;
    z-index: 1;
}

.hero-logo .main-logo {
    position: relative;
    margin-bottom: 30px;
}

.hero-logo .logo-circle {
    width: 200px;
    height: 200px;
    margin: 0 auto 20px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 3px solid rgba(255, 255, 255, 0.2);
    animation: heroLogoPulse 3s ease-in-out infinite;
}

.hero-logo .circuit-pattern {
    width: 100px;
    height: 100px;
}

.hero-logo .floating-letters {
    top: -60px;
    gap: 20px;
}

.hero-logo .letter {
    font-size: 36px;
    text-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
}

.hero-title {
    font-size: 4rem;
    font-weight: 900;
    margin-bottom: 20px;
    letter-spacing: 2px;
}

.gradient-text {
    background: linear-gradient(45deg, #ffffff, #60A5FA, #ffffff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradientShift 3s ease-in-out infinite;
}

.hero-subtitle {
    font-size: 1.5rem;
    margin-bottom: 40px;
    opacity: 0.9;
}

.hero-buttons {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
}

.btn {
    padding: 15px 30px;
    border: none;
    border-radius: 50px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    position: relative;
    overflow: hidden;
}

.btn-primary {
    background: var(--gradient-primary);
    color: white;
    box-shadow: 0 10px 30px rgba(107, 70, 193, 0.4);
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 40px rgba(138, 79, 255, 0.6);
}


.btn-secondary {
    background: transparent;
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-3px);
}

.scroll-indicator {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    animation: bounce 2s infinite;
}

.scroll-arrow {
    width: 30px;
    height: 30px;
    border-right: 2px solid white;
    border-bottom: 2px solid white;
    transform: rotate(45deg);
}

/* Animations */
@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

@keyframes fadeInOut {
    0%, 100% { opacity: 0.7; }
    50% { opacity: 1; }
}

@keyframes glow {
    from { box-shadow: 0 0 20px rgba(107, 70, 193, 0.5); }
    to { box-shadow: 0 0 30px rgba(139, 92, 246, 0.8); }
}

@keyframes floatShapes {
    0% { transform: translateY(100vh) rotate(0deg); }
    100% { transform: translateY(-100px) rotate(360deg); }
}

@keyframes heroLogoPulse {
    0%, 100% { transform: scale(1); box-shadow: 0 0 30px rgba(255, 255, 255, 0.3); }
    50% { transform: scale(1.05); box-shadow: 0 0 50px rgba(255, 255, 255, 0.5); }
}

@keyframes gradientShift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateX(-50%) translateY(0); }
    40% { transform: translateX(-50%) translateY(-10px); }
    60% { transform: translateX(-50%) translateY(-5px); }
}

/* Section Styles */
.section-header {
    text-align: center;
    margin-bottom: 60px;
}

.section-title {
    font-size: 3rem;
    font-weight: 900;
    margin-bottom: 20px;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.section-subtitle {
    font-size: 1.2rem;
    opacity: 0.8;
    max-width: 600px;
    margin: 0 auto;
}

/* About Section */
.about {
    padding: 100px 0;
    background: var(--bg-dark);
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
}

.about-text h3 {
    font-size: 2.5rem;
    margin-bottom: 20px;
    color: var(--light-blue);
}

.about-text p {
    font-size: 1.1rem;
    line-height: 1.8;
    margin-bottom: 40px;
    opacity: 0.9;
}

.stats {
    display: flex;
    gap: 40px;
    flex-wrap: wrap;
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 3rem;
    font-weight: 900;
    color: var(--light-blue);
    margin-bottom: 5px;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.8;
}

.tech-stack {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
}

.tech-item {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 20px;
    text-align: center;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.tech-item:hover {
    transform: translateY(-5px);
    background: rgba(107, 70, 193, 0.1);
    border-color: var(--secondary-purple);
}

.tech-item i {
    font-size: 2.5rem;
    margin-bottom: 10px;
    color: var(--light-blue);
}

.tech-item span {
    display: block;
    font-weight: 600;
}

/* Services Section */
.services {
    padding: 100px 0;
    background: linear-gradient(135deg, var(--bg-dark) 0%, #1E293B 100%);
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.service-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 40px 30px;
    text-align: center;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    opacity: 0;
    transition: all 0.3s ease;
    z-index: -1;
}

.service-card:hover::before {
    left: 0;
    opacity: 0.1;
}

.service-card:hover {
    transform: translateY(-10px);
    border-color: var(--secondary-purple);
    box-shadow: 0 20px 40px rgba(107, 70, 193, 0.3);
}

.service-icon {
    width: 80px;
    height: 80px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    font-size: 2rem;
    color: white;
}

.service-card h3 {
    font-size: 1.5rem;
    margin-bottom: 15px;
    color: var(--light-blue);
}

.service-card p {
    margin-bottom: 20px;
    opacity: 0.9;
    line-height: 1.6;
}

.service-features {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    justify-content: center;
}

.service-features span {
    background: rgba(96, 165, 250, 0.2);
    color: var(--light-blue);
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 0.9rem;
    border: 1px solid rgba(96, 165, 250, 0.3);
}

/* Portfolio Section */
.portfolio {
    padding: 100px 0;
    background: var(--bg-dark);
}

.portfolio-filters {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-bottom: 50px;
    flex-wrap: wrap;
}

.filter-btn {
    background: transparent;
    border: 2px solid rgba(255, 255, 255, 0.2);
    color: var(--text-light);
    padding: 10px 25px;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.filter-btn.active,
.filter-btn:hover {
    background: var(--gradient-primary);
    border-color: transparent;
    transform: translateY(-2px);
}

.portfolio-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
}

.portfolio-item {
    position: relative;
    border-radius: 15px;
    overflow: hidden;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.portfolio-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
}

.portfolio-image {
    height: 250px;
    overflow: hidden;
    position: relative;
}

.project-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.portfolio-item:hover .project-image {
    transform: scale(1.05);
}

.portfolio-video-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.6), rgba(20, 20, 40, 0.6));
    backdrop-filter: blur(2px);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.4s ease;
}

.portfolio-item:hover .portfolio-video-overlay {
    opacity: 1;
}

/* Pulse animation for play button */
@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(96, 165, 250, 0.7);
    }
    70% {
        box-shadow: 0 0 0 15px rgba(96, 165, 250, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(96, 165, 250, 0);
    }
}

.portfolio-item:hover .play-btn {
    animation: pulse 2s infinite;
}

.play-btn {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-blue), var(--light-blue));
    border: 3px solid rgba(255, 255, 255, 0.3);
    color: white;
    font-size: 1.8rem;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.play-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at center, rgba(255, 255, 255, 0.2) 0%, transparent 70%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.play-btn:hover::before {
    opacity: 1;
}

.play-btn:hover {
    transform: scale(1.15);
    box-shadow:
        0 10px 25px rgba(96, 165, 250, 0.5),
        0 0 0 10px rgba(96, 165, 250, 0.1),
        0 0 0 20px rgba(96, 165, 250, 0.05);
    border-color: rgba(255, 255, 255, 0.6);
}

.play-btn:active {
    transform: scale(1.05);
}

.play-btn i {
    margin-left: 3px; /* تعديل موضع أيقونة التشغيل */
    position: relative;
    z-index: 1;
}

.placeholder-image {
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    color: white;
    font-weight: 600;
}

.web-placeholder::before {
    content: '🌐 موقع ويب';
}

.ecommerce-placeholder::before {
    content: '🛒 متجر إلكتروني';
}

.learning-placeholder::before {
    content: '📚 منصة تعليمية';
}

.restaurant-placeholder::before {
    content: '🍽️ موقع مطعم';
}

.portfolio-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.9));
    padding: 30px 20px 20px;
    transform: translateY(100%);
    transition: all 0.3s ease;
}

.portfolio-item:hover .portfolio-overlay {
    transform: translateY(0);
}

.portfolio-overlay h4 {
    font-size: 1.3rem;
    margin-bottom: 10px;
    color: var(--light-blue);
}

.portfolio-overlay p {
    margin-bottom: 15px;
    opacity: 0.9;
}

.project-tech {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 15px;
}

.tech-tag {
    background: rgba(96, 165, 250, 0.2);
    color: var(--light-blue);
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
    border: 1px solid rgba(96, 165, 250, 0.3);
}

.portfolio-links {
    display: flex;
    gap: 10px;
}

.portfolio-link {
    width: 40px;
    height: 40px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-decoration: none;
    transition: all 0.3s ease;
}

.portfolio-link:hover {
    transform: scale(1.1);
}

/* Enhanced Video Modal */
.video-modal {
    display: none;
    position: fixed;
    z-index: 10000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.95), rgba(20, 20, 40, 0.95));
    backdrop-filter: blur(10px);
    align-items: center;
    justify-content: center;
    animation: modalFadeIn 0.3s ease-out;
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

.video-modal-content {
    position: relative;
    max-width: 95%;
    max-height: 95%;
    background: linear-gradient(145deg, #1a1a2e, #16213e);
    border-radius: 20px;
    overflow: hidden;
    box-shadow:
        0 25px 50px rgba(0, 0, 0, 0.7),
        0 0 0 1px rgba(96, 165, 250, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    animation: contentSlideIn 0.4s ease-out 0.1s both;
}

@keyframes contentSlideIn {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.video-header {
    background: linear-gradient(135deg, var(--primary-blue), var(--light-blue));
    padding: 20px 25px;
    color: white;
    position: relative;
    overflow: hidden;
}

.video-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="circuit" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse"><path d="M0 10h20M10 0v20" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23circuit)"/></svg>');
    opacity: 0.3;
}

.video-title {
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0;
    position: relative;
    z-index: 1;
}

.video-subtitle {
    font-size: 0.9rem;
    opacity: 0.9;
    margin: 5px 0 0 0;
    position: relative;
    z-index: 1;
}

.video-close {
    position: absolute;
    top: 15px;
    right: 20px;
    color: white;
    font-size: 1.5rem;
    font-weight: bold;
    cursor: pointer;
    z-index: 10001;
    background: rgba(0, 0, 0, 0.3);
    width: 35px;
    height: 35px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.video-close:hover {
    background: rgba(255, 0, 0, 0.8);
    transform: scale(1.1) rotate(90deg);
    box-shadow: 0 5px 15px rgba(255, 0, 0, 0.3);
}

/* Custom Video Player */
.custom-video-player {
    position: relative;
    background: #000;
    border-radius: 10px;
    overflow: hidden;
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.3),
        inset 0 0 0 1px rgba(96, 165, 250, 0.2);
}

.custom-video-player video {
    width: 100%;
    height: auto;
    max-height: 70vh;
    display: block;
    cursor: pointer;
}

.video-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, 0.3);
    opacity: 0;
    transition: opacity 0.3s ease;
    cursor: pointer;
}

.custom-video-player:hover .video-overlay {
    opacity: 1;
}

.play-pause-btn {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-blue), var(--light-blue));
    border: 3px solid rgba(255, 255, 255, 0.8);
    color: white;
    font-size: 2rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
}

.play-pause-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 15px 40px rgba(96, 165, 250, 0.6);
}

.play-pause-btn i {
    margin-left: 3px;
}

.video-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: none;
    align-items: center;
    justify-content: center;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-top: 3px solid var(--light-blue);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Custom Video Controls */
.custom-controls {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.9));
    padding: 20px;
    transition: all 0.3s ease;
}

.progress-container {
    margin-bottom: 15px;
}

.progress-bar {
    position: relative;
    height: 6px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
    cursor: pointer;
    margin-bottom: 10px;
}

.progress-filled {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-blue), var(--light-blue));
    border-radius: 3px;
    width: 0%;
    transition: width 0.1s ease;
    box-shadow: 0 0 10px rgba(96, 165, 250, 0.5);
}

.progress-handle {
    position: absolute;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 16px;
    height: 16px;
    background: white;
    border-radius: 50%;
    cursor: pointer;
    left: 0%;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    transition: all 0.2s ease;
}

.progress-handle:hover {
    transform: translate(-50%, -50%) scale(1.2);
}

.time-display {
    display: flex;
    justify-content: space-between;
    color: white;
    font-size: 0.9rem;
    font-weight: 500;
}

.control-buttons {
    display: flex;
    align-items: center;
    gap: 15px;
}

.control-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    backdrop-filter: blur(10px);
}

.control-btn:hover {
    background: rgba(96, 165, 250, 0.8);
    transform: scale(1.1);
}

.speed-btn {
    border-radius: 8px;
    width: auto;
    padding: 0 12px;
    font-size: 0.9rem;
    font-weight: 600;
}

.volume-slider {
    display: flex;
    align-items: center;
    margin-left: 10px;
}

.volume-bar {
    width: 80px;
    height: 4px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;
    cursor: pointer;
    position: relative;
}

.volume-filled {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-blue), var(--light-blue));
    border-radius: 2px;
    width: 80%;
    transition: width 0.2s ease;
}

.video-info {
    background: rgba(26, 26, 46, 0.95);
    padding: 20px 25px;
    border-top: 1px solid rgba(96, 165, 250, 0.2);
}

.video-tech-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 15px;
}

.video-tech-tag {
    background: rgba(96, 165, 250, 0.2);
    color: var(--light-blue);
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
    border: 1px solid rgba(96, 165, 250, 0.3);
    transition: all 0.3s ease;
}

.video-tech-tag:hover {
    background: rgba(96, 165, 250, 0.3);
    transform: translateY(-2px);
}

/* Responsive Custom Video Player */
@media (max-width: 768px) {
    .video-modal-content {
        max-width: 98%;
        max-height: 98%;
        margin: 10px;
    }

    .video-header {
        padding: 15px 20px;
    }

    .video-title {
        font-size: 1.2rem;
    }

    .video-info {
        padding: 15px 20px;
    }

    .custom-video-player video {
        max-height: 50vh;
    }

    .play-pause-btn {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }

    .custom-controls {
        padding: 15px;
    }

    .control-buttons {
        gap: 10px;
    }

    .control-btn {
        width: 35px;
        height: 35px;
        font-size: 0.9rem;
    }

    .volume-bar {
        width: 60px;
    }

    .progress-handle {
        width: 14px;
        height: 14px;
    }

    .time-display {
        font-size: 0.8rem;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .hamburger {
        display: flex;
    }

    .nav-menu {
        position: fixed;
        top: 70px;
        right: -100%;
        width: 100%;
        height: calc(100vh - 70px);
        background: rgba(15, 23, 42, 0.98);
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        padding-top: 50px;
        transition: right 0.3s ease;
    }

    .nav-menu.active {
        right: 0;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-subtitle {
        font-size: 1.2rem;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .hero-logo .logo-circle {
        width: 150px;
        height: 150px;
    }

    .hero-logo .circuit-pattern {
        width: 75px;
        height: 75px;
    }

    .hero-logo .letter {
        font-size: 24px;
    }

    .about-content {
        grid-template-columns: 1fr;
        gap: 40px;
    }

    .stats {
        justify-content: center;
    }

    .tech-stack {
        grid-template-columns: repeat(2, 1fr);
    }

    .section-title {
        font-size: 2.5rem;
    }

    .portfolio-grid {
        grid-template-columns: 1fr;
    }

    .language-switcher {
        margin-left: 0;
        margin-right: 0;
        margin-top: 10px;
    }

    .nav-container {
        flex-wrap: wrap;
    }

    [dir="ltr"] .nav-menu {
        left: -100%;
        right: auto;
    }

    [dir="ltr"] .nav-menu.active {
        left: 0;
    }
}

/* Contact Section */
.contact {
    padding: 100px 0;
    background: linear-gradient(135deg, #1E293B 0%, var(--bg-dark) 100%);
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: start;
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 20px;
    padding: 25px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.contact-item:hover {
    transform: translateX(10px);
    background: rgba(107, 70, 193, 0.1);
    border-color: var(--secondary-purple);
}

.contact-icon {
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    flex-shrink: 0;
}

.contact-details h4 {
    font-size: 1.2rem;
    margin-bottom: 5px;
    color: var(--light-blue);
}

.contact-details p {
    opacity: 0.9;
    font-size: 1rem;
}

.contact-form {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 40px;
    backdrop-filter: blur(10px);
}

.form-group {
    margin-bottom: 25px;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 15px 20px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    color: var(--text-light);
    font-size: 1rem;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--secondary-purple);
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 0 20px rgba(107, 70, 193, 0.3);
}

.form-group input::placeholder,
.form-group textarea::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

/* Footer */
.footer {
    background: var(--bg-dark);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding: 60px 0 30px;
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 40px;
    flex-wrap: wrap;
    gap: 30px;
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: 15px;
}

.footer-logo .logo-circle {
    width: 50px;
    height: 50px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: glow 2s ease-in-out infinite alternate;
}

.footer-logo .circuit-pattern {
    width: 25px;
    height: 25px;
}

.footer-logo .logo-text {
    font-size: 1.5rem;
    font-weight: 900;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.footer-text {
    text-align: center;
    flex-grow: 1;
}

.footer-text p:first-child {
    font-size: 1.2rem;
    font-weight: 700;
    letter-spacing: 2px;
    margin-bottom: 5px;
    color: var(--light-blue);
}

.footer-text p:last-child {
    opacity: 0.8;
    font-size: 0.9rem;
}

.social-links {
    display: flex;
    gap: 15px;
}

.social-links a {
    width: 45px;
    height: 45px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-light);
    text-decoration: none;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.social-links a:hover {
    background: var(--gradient-primary);
    border-color: transparent;
    transform: translateY(-3px);
    box-shadow: 0 10px 20px rgba(107, 70, 193, 0.4);
}

.footer-bottom {
    text-align: center;
    padding-top: 30px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    opacity: 0.7;
}

/* Additional Responsive Styles */
@media (max-width: 768px) {
    .contact-content {
        grid-template-columns: 1fr;
        gap: 40px;
    }

    .contact-form {
        padding: 30px 20px;
    }

    .footer-content {
        flex-direction: column;
        text-align: center;
    }

    .footer-text {
        order: -1;
    }
}

.site-logo, .footer-logo-img {
    max-width: 120px;
    height: auto;
    display: block;
    margin: 0 auto;
}

.site-logo, .footer-logo-img {
    filter: drop-shadow(0 0 10px rgba(138, 79, 255, 0.6));
}
